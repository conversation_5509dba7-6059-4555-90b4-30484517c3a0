"use client";
import React from "react";
import { Loader2 } from "lucide-react";
import { toast } from "../ui/use-toast";
import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { ToolClassify } from "../ToolClassify";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "../../../navigation";
import { CustomToolForm } from "../CustomToolForm";
import { deleteDatasTool } from "@/app/api/indexedDB";
import { ITool, toolList } from "@/constant/tool_list";
import { IoMdCloseCircleOutline } from "react-icons/io";
import { useAppDispatch, useAppSelector } from "@/app/store/hooks";
import { selectGlobal, setGlobalState } from "@/app/store/globalSlice";
import {
  debounce,
  getLanguage,
  getLocalStorage,
  setLocalStorage,
} from "@/lib/utils";
import {
  deleteCustomToolDataKey,
  getAllCustomToolData,
} from "@/app/api/customTool/indexedDB";
import {
  deleteClassifyData,
  getAllClassifyData,
  IClassify,
} from "@/app/api/classify/indexedDB";
import {
  CARD_RECENTLY_USED,
  classify,
  INPUT_PLACEHOLDER,
  LANGUAGE_LIBRARY,
  MAIN_PAGE_HEADING,
} from "@/constant/language";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";

export default function Home() {
  const router = useRouter();
  const global = useAppSelector(selectGlobal);
  const dispatch = useAppDispatch();

  const [type, setType] = useState("All");
  const [open, setOpen] = useState(false);
  const [delLoad, setDelLoad] = useState(false);
  const [search, setSearch] = useState({ query: "", querying: false });
  const [recentlyUsedData, setRecentlyUsedData] = useState<Array<ITool>>([]);
  const [toolData, setToolData] = useState({
    list: toolList,
    searchList: {} as any,
  });

  const showBrand = process.env.NEXT_PUBLIC_SHOW_BRAND === "true";

  // Read the current user's language
  useEffect(() => {
    const lang = getLanguage();
    dispatch(setGlobalState({ language: lang }));
  }, []);

  // Read recently used tools
  useEffect(() => {
    onGetRecentlyUsedData();
  }, [toolData]);

  useEffect(() => {
    const tempData = JSON.parse(JSON.stringify(toolList));
    // Traverse each key of the tempData object and sort the array by ID
    Object.keys(tempData).forEach((key) => {
      tempData[key] = tempData[key].sort((a: any, b: any) => {
        const idA = `${a.id}`.substring(`${a.id}`.lastIndexOf("_") + 1);
        const idB = `${b.id}`.substring(`${b.id}`.lastIndexOf("_") + 1);
        return +idB - +idA;
      });
    });
    if (global.customTool.length) {
      for (let index = 0; index < global.customTool.length; index++) {
        const presetToolKeys = Object.keys(tempData);
        const element = global.customTool[index];
        if (presetToolKeys.indexOf(element.classify_key) > -1) {
          tempData[element.classify_key].unshift(element);
        } else {
          tempData[element.classify_key] = [element];
        }
      }
    }
    setToolData((v) => ({ ...v, list: tempData }));
  }, [global.customTool]);

  const onGetRecentlyUsedData = () => {
    // Get a list of recently used tools
    const recentlyUsedDataTemp = JSON.parse(
      getLocalStorage("recentlyUsedData") || "[]"
    );
    if (recentlyUsedDataTemp && recentlyUsedDataTemp.length > 0) {
      const arr: Array<ITool> = [];
      recentlyUsedDataTemp.forEach((item: ITool) => {
        const temp = toolData.list[item?.classify?.english]?.find(
          (f) => f.id === item.id
        );
        if (temp?.id) {
          arr.push(temp);
        }
      });
      setRecentlyUsedData(arr);
    } else {
      setRecentlyUsedData([]);
    }
  };

  const onSearch = (query: string) => {
    setSearch({ query, querying: true });
    const toolDataTemp: { [key: string]: Array<ITool> } = {};
    if (query || type) {
      for (const key in toolData.list) {
        toolData.list[key].forEach((item: ITool) => {
          const { name, describe } = item;
          if (type !== "All") {
            if (
              (name.chinese.indexOf(query) > -1 ||
                name.english.indexOf(query) > -1 ||
                describe.chinese.indexOf(query) > -1 ||
                describe.japanese.indexOf(query) > -1 ||
                describe.english.indexOf(query) > -1) &&
              type === key
            ) {
              if (!toolDataTemp[key]) toolDataTemp[key] = [];
              toolDataTemp[key].push(item);
            }
          } else {
            if (
              name.chinese.indexOf(query) > -1 ||
              name.english.indexOf(query) > -1 ||
              describe.chinese.indexOf(query) > -1 ||
              describe.japanese.indexOf(query) > -1 ||
              describe.english.indexOf(query) > -1
            ) {
              if (!toolDataTemp[key]) toolDataTemp[key] = [];
              toolDataTemp[key].push(item);
            }
          }
        });
      }
      if (Object.keys(toolDataTemp).length) {
        setToolData((v) => ({ ...v, searchList: toolDataTemp }));
      }
    } else {
      setToolData((v) => ({ ...v, searchList: {} }));
    }
    setSearch((v) => ({ ...v, querying: false }));
  };

  const onRenderingToolCard = (
    data: Array<Pick<ITool, "id" | "title" | "url" | "name" | "describe">>
  ) => {
    return (
      <div className="tool-cards-grid">
        {data.map((item) => (
          <div
            key={item.id}
            onClick={() => router.push(`/${item.title}`)}
            className="tool-card"
          >
            <div className="tool-card-icon">
              <img
                className="w-full h-full object-cover rounded-md"
                src={item.url}
                alt={item.name[global.language]}
              />
            </div>
            <div className="tool-card-content">
              <div className="tool-card-title">
                {item.name[global.language]}
              </div>
              <div className="tool-card-description">
                {item.describe[global.language]}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const onInputChange = debounce(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      onSearch(event.target.value);
    },
    300
  );

  const onDelCustomTool = (params: IClassify) => {
    const onDelData = async () => {
      setDelLoad(true);
      // Delete custom category
      await deleteClassifyData(params.id);
      const result = await deleteCustomToolDataKey(params.classify_key);
      console.log(result);
      if (result.length) {
        await deleteDatasTool(result.map((m) => m.id));
      }
      // Delete Recently Used Data
      const recentlyUsedDataTemp: ITool[] = JSON.parse(
        getLocalStorage("recentlyUsedData") || "[]"
      );
      const temp: ITool[] = [];
      if (recentlyUsedDataTemp && recentlyUsedDataTemp.length) {
        for (let i = 0; i < recentlyUsedDataTemp.length; i++) {
          const element = recentlyUsedDataTemp[i];
          if (element.classify.english !== params.english) {
            temp.push(element);
          }
        }
        setLocalStorage("recentlyUsedData", JSON.stringify(temp));
      }
      const classifyData = await getAllClassifyData();
      const customToolData = await getAllCustomToolData();
      onGetRecentlyUsedData();
      dispatch(
        setGlobalState({ classify: classifyData, customTool: customToolData })
      );
      toast({
        duration: 2000,
        description: LANGUAGE_LIBRARY[global.language]["删除成功"],
      });
      setDelLoad(false);
      setOpen(false);
    };
    return (
      <Dialog open={open} onOpenChange={(open) => !delLoad && setOpen(open)}>
        <DialogTrigger asChild>
          <IoMdCloseCircleOutline
            className="text-lg ml-1 hover:text-red-600"
            onClick={(event) => {
              event.stopPropagation();
              setOpen(true);
            }}
          />
        </DialogTrigger>
        <DialogContent
          className="sm:max-w-[425px]"
          onClick={(e) => e.stopPropagation()}
        >
          <DialogHeader>
            <DialogTitle>
              {LANGUAGE_LIBRARY[global.language]["删除分类"]} “
              {params[global.language]}”{" "}
            </DialogTitle>
            <DialogDescription>
              {
                LANGUAGE_LIBRARY[global.language][
                  "是否确认删除此分类，此分类下的模版都会被删除。"
                ]
              }
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <DialogClose type="button" disabled={delLoad}>
              {LANGUAGE_LIBRARY[global.language]["取消"]}
            </DialogClose>
            <Button type="submit" className=" " onClick={onDelData}>
              {delLoad ? (
                <Loader2
                  className="animate-spin"
                  style={{ width: 18, height: 18 }}
                />
              ) : (
                LANGUAGE_LIBRARY[global.language]["确认"]
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  const onDelClassify = async (params: IClassify) => {
    await deleteClassifyData(params.id);
    const classifyData = await getAllClassifyData();
    dispatch(setGlobalState({ classify: classifyData }));
  };

  return (
    <div className="md:py-10 lg:py-14 py-3 min-h-screen relative">
      <div className="main-container h-full flex flex-col mx-auto my-0 mb-9">
        {/* Main Page Heading */}
        <h1 className="main-heading">
          {MAIN_PAGE_HEADING[global.language]}
        </h1>
        
        <div className="w-full max-w-[1286px] mx-auto my-0 flex justify-center transition-all">
          <Input
            className="search-input"
            placeholder={INPUT_PLACEHOLDER[global.language]}
            onChange={onInputChange}
          />
        </div>
        <div className="trending-search-container">
          {/* 预设分类 */}
          {[
            {
              vietnamese: "Tất cả",
              chinese: "全部",
              english: "All",
              japanese: "すべて",
            },
            ...classify,
          ].map((item, index) => {
            return (
              <Button
                key={item.english}
                onClick={() => {
                  setType(item.english);
                }}
                className={`trending-search-tab ${
                  type === item.english ? "selected" : ""
                }`}
              >
                {item[global.language]}
              </Button>
            );
          })}
          {/* 自定义分类 */}
          {global.classify.map((item, index) => {
            return (
              <Button
                key={item.english}
                onClick={() => {
                  setType(item.english);
                }}
                className={`trending-search-tab ${
                  type === item.english ? "selected" : ""
                }`}
              >
                {item[global.language]}
                {toolData.list[item.classify_key] ? (
                  onDelCustomTool(item)
                ) : (
                  <IoMdCloseCircleOutline
                    className="text-lg ml-1 hover:text-red-600"
                    onClick={(event) => {
                      event.stopPropagation();
                      onDelClassify(item);
                    }}
                  />
                )}
              </Button>
            );
          })}
        </div>
        <div className="custom-buttons-container">
          <CustomToolForm />
          <ToolClassify />
        </div>
        {/* 最近使用工具 */}
        {/* 最近使用工具 - only render container if there is content */}
        {recentlyUsedData.length && !search.query && type === "All" && (
          <div className="tool-section-container">
            <div className="font-bold my-3 text-lg">
              {CARD_RECENTLY_USED[global.language]}
            </div>
            {onRenderingToolCard(recentlyUsedData)}
          </div>
        )}
        {/* 工具列表 */}
        {[...global.classify, ...classify]
          .filter((f) => (type === "All" ? f : f.english === type))
          .map((item) => {
            const hasList = !search.query && toolData.list[item.english]?.length;
            const hasSearchList = search.query && toolData.searchList[item.english]?.length;
            if (!hasList && !hasSearchList) return null;
            return (
              <div key={item.english} className="tool-section-container">
                {hasList && (
                  <>
                    <div className="font-bold my-3 text-lg">
                      {item[global.language]}
                    </div>
                    {onRenderingToolCard(toolData.list[item.english])}
                  </>
                )}
                {hasSearchList && (
                  <>
                    <div className="font-bold my-3 text-lg">
                      {item[global.language]}
                    </div>
                    {onRenderingToolCard(toolData.searchList[item.english])}
                  </>
                )}
              </div>
            );
          })}
      </div>
    </div>
  );
}
