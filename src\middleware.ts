import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export default async function middleware(request: NextRequest) {
  const url = request.nextUrl.clone();
  const token = request.nextUrl.searchParams.get("token");

  // ⬇️ If token is present in the URL, set it as cookie and redirect
  if (token) {
    url.searchParams.delete("token"); // Clean the URL

    const response = NextResponse.redirect(url);

    // ⬇️ Set cookie named 'api-key' with 7 days expiration
    response.cookies.set("api-key", token, {
      path: "/",
      httpOnly: true,
      sameSite: "lax",
      maxAge: 60 * 60 * 24 * 7, // 7 days in seconds
    });

    return response;
  }

  const response = NextResponse.next();

  // ⬇️ Locale handling
  const langParam = request.nextUrl.searchParams.get("lang");
  const headerLang = request.headers.get("x-locale");
  let locale = langParam ? langParam.split("-")[0] : headerLang;

  if (!["zh", "en", "ja", "vi"].includes(locale || "")) {
    locale = "en";
  }

  const currentLocale = url.pathname.split("/")[1];
  if (!["zh", "en", "ja", "vi"].includes(currentLocale)) {
    url.pathname = `/${locale}${url.pathname}`;
    url.searchParams.delete("lang");

    return NextResponse.redirect(url);
  } else {
    locale = currentLocale;
  }

  response.headers.set("x-locale", locale || "en");

  return response;
}

export const config = {
  matcher: ["/((?!api|_next|.*\\..*).*)"],
};
