{"name": "302_copywriting_assistant", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@reduxjs/toolkit": "^2.2.6", "ai": "^3.4.5", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cross-env": "^7.0.3", "dayjs": "^1.11.12", "idb": "^8.0.0", "ky": "^1.7.2", "lucide-react": "^0.408.0", "next": "14.2.5", "next-intl": "^3.17.6", "next-themes": "^0.4.6", "openai": "^4.65.0", "papaparse": "^5.4.1", "penpal": "^7.0.4", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.52.1", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "react-redux": "^9.1.2", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.11", "@types/papaparse": "^5.3.14", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}