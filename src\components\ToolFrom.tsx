import { z } from "zod";
import { Input } from "./ui/input";
import { Button } from "./ui/button";
import { Loader2 } from "lucide-react";
import { Textarea } from "./ui/textarea";
import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import { ITool } from "@/constant/tool_list";
import { zodResolver } from "@hookform/resolvers/zod";
import { getLocalStorage, setLocalStorage } from "@/lib/utils";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "./ui/form";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import {
  CLEAR_CONTENT_BUTTON,
  FROM_LABEL,
  LANGUAGE_LIST,
  OUTPUT_LANGUAGE,
  PLEASE_ENTER,
  PLEASE_SELECT,
  SUBMIT_BUTTON,
} from "@/constant/language";

interface IProps {
  dataSource: ITool & { prompt?: string };
  language: "chinese" | "english" | "japanese" | "vietnamese";
  onOk: (value: any) => void;
}

export default function ToolFrom(props: IProps) {
  const [load, setLoad] = useState(false);
  const { language, dataSource, onOk } = props;
  const [outputLanguage, setOutputLanguage] = useState("Vietnamese");
  const [hasContent, setHasContent] = useState(false);

  useEffect(() => {
    const outputLanguageTemp = getLocalStorage("language");
    if (outputLanguageTemp) {
      setOutputLanguage(outputLanguageTemp);
    } else {
      setLocalStorage(
        "language",
        language.replace(/^[a-z]/, (match) => match.toUpperCase())
      );
    }
  }, []);

  const onReminderInformation = (type: string, key: string) => {
    switch (type) {
      case "Select":
        return `${PLEASE_SELECT[language]}${FROM_LABEL[key][language]}`;
      default:
        return `${PLEASE_ENTER[language]}${FROM_LABEL[key][language]}`;
    }
  };

  const onFormSchema = (defaultValues?: boolean) => {
    let obj: { [key: string]: any } = {};
    for (const key in dataSource.from) {
      if (defaultValues) {
        obj[key] = "";
      } else {
        const message = onReminderInformation(dataSource.from[key].type, key);
        obj[key] = z.string().min(1, { message });
      }
    }
    return obj;
  };
  const formSchema = z.object(onFormSchema());

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: onFormSchema(true),
  });

  // Watch form values to enable/disable button
  const watchedValues = form.watch();

  useEffect(() => {
    const hasAnyContent = Object.values(watchedValues).some(value =>
      typeof value === 'string' && value.trim().length > 0
    );
    setHasContent(hasAnyContent);
  }, [watchedValues, setHasContent]);

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    if (load) return;
    setLoad(true);
    await onOk({ ...data, language: outputLanguage });
    setLoad(false);
  };

  const onChangeOutputLanguage = (value: string) => {
    if (value) {
      setOutputLanguage(value);
      setLocalStorage("language", value);
    }
  };

  const onRenderingSelect = (
    data: Array<{
      vietnamese: string;
      chinese: string;
      english: string;
      japanese: string;
      value?: string;
    }>,
    placeholder: string
  ) => {
    return (
      <>
        <SelectTrigger>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            {data.map((item) => (
              <SelectItem
                key={item.english}
                value={item?.value || item.english}
              >
                {item[language]}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </>
    );
  };

  const onRenderingOperationForm = (field: any, key: string) => {
    const placeholder = onReminderInformation(dataSource.from[key].type, key);
    const minHeight = dataSource.from[key]?.isBig ? "min-h-40" : "min-h-20";
    switch (dataSource.from[key].type) {
      case "Input":
        return <Input placeholder={placeholder} {...field} />;
      case "Textarea":
        return (
          <Textarea
            className={minHeight}
            placeholder={placeholder}
            {...field}
          />
        );
      case "Select":
        return (
          <Select
            onValueChange={field.onChange}
            value={field.value}
            disabled={load}
          >
            {onRenderingSelect(dataSource.from[key]?.list || [], placeholder)}
          </Select>
        );
      default:
        break;
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Content & Tone Container */}
      <div
        className="flex flex-col"
        style={{
          width: 480,
          height: 689,
          padding: 24,
          gap: 24
        }}
      >
        <Form {...form}>
          <div className="flex flex-col" style={{ gap: 24 }}>
            {Object.keys(dataSource.from).map((key: string) => (
              <FormField
                key={key}
                disabled={load}
                control={form.control}
                name={key}
                render={({ field }: any) => (
                  <FormItem>
                    <FormLabel className="font-bold">
                      {FROM_LABEL[key][language]}
                    </FormLabel>
                    <FormControl>
                      {onRenderingOperationForm(field, key)}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ))}
          </div>
        </Form>
      </div>

      {/* Language Selection & Clear Content Container */}
      <div
        className="flex justify-between items-center"
        style={{
          width: 480,
          height: 72,
          borderTop: '0.75px solid var(--border)',
          paddingTop: 12,
          paddingBottom: 12,
          paddingLeft: 24,
          paddingRight: 24
        }}
      >
        <div
          className={`md:w-32 w-24 ${
            dataSource?.prompt ? "hidden" : "block"
          }`}
        >
          <Select
            onValueChange={onChangeOutputLanguage}
            value={outputLanguage}
            disabled={load}
          >
            {onRenderingSelect(LANGUAGE_LIST, OUTPUT_LANGUAGE[language])}
          </Select>
        </div>
        <Form {...form}>
          <Button
            disabled={load}
            type="button"
            variant="ghost"
            onClick={() => form.reset()}
          >
            {CLEAR_CONTENT_BUTTON[language]}
          </Button>
        </Form>
      </div>

      {/* Start Generating Button Container */}
      <div
        className="flex flex-col"
        style={{
          width: 480,
          height: 96,
          borderTop: '0.75px solid var(--border)',
          padding: 24,
          gap: 10
        }}
      >
        <Form {...form}>
          <Button
            type="submit"
            disabled={load || !hasContent}
            className="btn-primary-custom"
            onClick={form.handleSubmit(onSubmit)}
          >
            {load ? (
              <Loader2
                className="animate-spin"
                style={{ width: 18, height: 18 }}
              />
            ) : (
              SUBMIT_BUTTON[dataSource.submitButton][language]
            )}
          </Button>
        </Form>
      </div>
    </div>
  );
}
