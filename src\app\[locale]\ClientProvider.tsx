"use client";

import { Provider } from "react-redux";
import store from "../store";
import { useEffect } from "react";
import { ThemeProvider } from "next-themes";

const ClientProvider = ({ children }: { children: React.ReactNode }) => {
  // useEffect(() => {
  //   if (window.parent !== window && document.referrer) {
  //     try {
  //       const parentOrigin = new URL(document.referrer).origin;
  //       const allowedOrigins: string[] = process.env.NEXT_PUBLIC_PARENT_URLS
  //         ? (process.env.NEXT_PUBLIC_PARENT_URLS || "")
  //             .split(",")
  //             .map((s) => new URL(s.trim()).origin)
  //         : [];
  //       if (!allowedOrigins.includes(parentOrigin)) {
  //         window.location.replace("about:blank");
  //       }
  //     } catch {
  //       window.location.replace("about:blank");
  //     }
  //   } else {
  //     window.location.replace("about:blank");
  //   }
    // const width = document.body.clientWidth;
    // const showBrand = process.env.NEXT_PUBLIC_SHOW_BRAND === "true";
    // if (width > 768 && showBrand) {
    //   const script = document.createElement("script");
    //   script.src =
    //     "https://assets.salesmartly.com/js/project_177_61_1649762323.js";
    //   script.async = true;
    //   document.body.appendChild(script);
    // }
  // }, []);
  return (
    <Provider store={store}>
      <ThemeProvider attribute={"class"} defaultTheme="system">
        {children}
      </ThemeProvider>
    </Provider>
  );
};

export default ClientProvider;
