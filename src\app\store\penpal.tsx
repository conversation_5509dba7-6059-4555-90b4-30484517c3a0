"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { connect, WindowMessenger, Connection, RemoteProxy } from "penpal";

export type ParentMethods = {
  handleToolUsageResponse(request: {
    actionName: string;
    success: boolean;
    message?: string;
    metadata?: Record<string, string | number | boolean>;
  }): Promise<void>;
  parentPing(): Promise<string>;
  verifyToolUsage(request: {
    actionName: string;
    metadata?: Record<string, string | number | boolean>;
  }): Promise<any>;
};

type PenpalContextType = {
  parentMethods: RemoteProxy<ParentMethods> | null;
  connection: Connection<ParentMethods> | null;
};

const PenpalContext = createContext<PenpalContextType>({
  parentMethods: null,
  connection: null,
});

export const usePenpal = () => useContext(PenpalContext);

export const PenpalProvider = ({ children }: { children: React.ReactNode }) => {
  const [parentMethods, setParentMethods] = useState<ParentMethods | null>(
    null
  );
  const [connection, setConnection] =
    useState<Connection<ParentMethods> | null>(null);

  useEffect(() => {
    const messenger = new WindowMessenger({
      remoteWindow: window.parent,
      allowedOrigins: [
        ...(process.env.NEXT_PUBLIC_PARENT_URLS ?? "").split(","),
      ],
    });

    const conn = connect<ParentMethods>({
      messenger,
      methods: {
        childPing() {
          return "pong from child";
        },
      },
    });

    conn.promise
      .then((methods) => {
        setParentMethods(methods);
        console.log("[Penpal] Connected to parent");
      })
      .catch((err) => {
        console.error("[Penpal] Failed to connect to parent:", err);
      });

    setConnection(conn);

    return () => conn.destroy();
  }, []);

  return (
    <PenpalContext.Provider value={{ parentMethods, connection }}>
      {children}
    </PenpalContext.Provider>
  );
};
