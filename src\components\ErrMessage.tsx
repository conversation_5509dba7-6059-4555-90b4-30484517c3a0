export function ErrMessage(
  err_code: number,
  language: "chinese" | "english" | "japanese" | "vietnamese"
) {
  switch (err_code) {
    case -10002:
      return language === "chinese" ? (
        <div className="font-bold">该工具已禁用/删除</div>
      ) : language === "japanese" ? (
        <div className="font-bold">
          このツールは無効化されました/削除されました。
        </div>
      ) : language === "vietnamese" ? (
        <div className="font-bold">Công cụ này đã bị vô hiệu hóa hoặc xóa</div>
      ) : (
        <div className="font-bold">This tool is disabled / deleted.</div>
      );
    case -10003:
      return language === "chinese" ? (
        <div className="font-bold">网络错误，请稍后重试</div>
      ) : language === "japanese" ? (
        <div className="font-bold">
          ネットワークエラーが発生しました。後でもう一度お試しください。
        </div>
      ) : language === "vietnamese" ? (
        <div className="font-bold">Lỗi mạng, vui lòng thử lại sau.</div>
      ) : (
        <div className="font-bold">Network error, please try again later.</div>
      );
    case -10004:
      return language === "chinese" ? (
        <div className="font-bold">账户余额不足</div>
      ) : language === "japanese" ? (
        <div className="font-bold">アカウントの残高が不足しています。</div>
      ) : language === "vietnamese" ? (
        <div className="font-bold">Số dư tài khoản không đủ</div>
      ) : (
        <div className="font-bold">Insufficient account balance.</div>
      );
    case -10006:
      return language === "chinese" ? (
        <div className="font-bold">账户总额度已达上限</div>
      ) : language === "japanese" ? (
        <div className="font-bold">
          このツールの総クォータが最大限度に達しました。
        </div>
      ) : language === "vietnamese" ? (
        <div className="font-bold">Tổng hạn mức tài khoản đã đạt giới hạn</div>
      ) : (
        <div className="font-bold">
          This tool's total quota reached maximum limit.
        </div>
      );
    case -10007:
      return language === "chinese" ? (
        <div className="font-bold">账户日额度已达上限</div>
      ) : language === "japanese" ? (
        <div className="font-bold">
          このツールの日次クォータが最大限度に達しました。
        </div>
      ) : language === "vietnamese" ? (
        <div className="font-bold">
          Hạn mức ngày của tài khoản đã đạt giới hạn
        </div>
      ) : (
        <div className="font-bold">
          This tool's daily quota reached maximum limit.
        </div>
      );
    case -10008:
      return language === "chinese" ? (
        <div className="font-bold">当前无可用通道</div>
      ) : language === "japanese" ? (
        <div className="font-bold">現在利用可能なチャネルがありません。</div>
      ) : language === "vietnamese" ? (
        <div className="font-bold">Hiện không có kênh khả dụng</div>
      ) : (
        <div className="font-bold">No available channels at the moment.</div>
      );
    case -10009:
      return language === "chinese" ? (
        <div className="font-bold">不支持当前API功能</div>
      ) : language === "japanese" ? (
        <div className="font-bold">現在のAPI機能はサポートされていません。</div>
      ) : language === "vietnamese" ? (
        <div className="font-bold">
          Chức năng API hiện tại không được hỗ trợ
        </div>
      ) : (
        <div className="font-bold">API function is not supported.</div>
      );
    case -10012:
      return language === "chinese" ? (
        <div className="font-bold">该免费工具在本小时的额度已达上限</div>
      ) : language === "japanese" ? (
        <div className="font-bold">
          この無料ツールは今時間の上限に達しました。
        </div>
      ) : language === "vietnamese" ? (
        <div className="font-bold">
          Công cụ miễn phí này đã đạt giới hạn trong giờ này
        </div>
      ) : (
        <div className="font-bold">
          This free tool's hour quota reached maximum limit.
        </div>
      );
    case -1024:
      return language === "chinese" ? (
        <div className="font-bold">AI接口连接超时，请稍后重试</div>
      ) : language === "japanese" ? (
        <div className="font-bold">
          AIインターフェイスの接続がタイムアウトしました。後でもう一度お試しください。
        </div>
      ) : language === "vietnamese" ? (
        <div className="font-bold">
          Kết nối giao diện AI bị hết thời gian, vui lòng thử lại sau
        </div>
      ) : (
        <div className="font-bold">
          AI interface connection timeout, please try again later.
        </div>
      );
    default:
      return language === "chinese" ? (
        <div className="font-bold ml-2">未知错误</div>
      ) : language === "japanese" ? (
        <div className="font-bold ml-2">不明なエラーが発生しました。</div>
      ) : language === "vietnamese" ? (
        <div className="font-bold ml-2">Lỗi không xác định</div>
      ) : (
        <div className="font-bold ml-2">Unknown Error.</div>
      );
  }
}
