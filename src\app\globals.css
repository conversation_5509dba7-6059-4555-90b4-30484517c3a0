@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --page-background: 0 0% 100%;
    --background: 300 50% 100%;
    --foreground: 0 0% 10%;
    --card: 0 0% 95%;
    --card-foreground: 0 0% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10% --primary: 0 0% 10%;
    --primary: 222.2deg 47.4% 11.2%;
    --primary-foreground: 210deg 40% 98%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 10%;
    --muted: 0 0% 95%;
    --muted-foreground: 0 0% 45%;
    --accent: 0 0% 90%;
    --accent-foreground: 0 0% 10%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 85%;
    --input: 0 0% 85%;
    --ring: 0 0% 60%;
  
    --radius: 0.5rem;
    --chart-1: 173 58% 39%;
    --chart-2: 12 76% 61%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --sidebar-foreground: 0, 0%, 14.5%;
    --sidebar-primary: 0, 0%, 20.5%;
    --sidebar-primary-foreground: 0, 0%, 98.5%;
    --sidebar-accent: 0, 0%, 97%;
    --sidebar-accent-foreground: 0, 0%, 20.5%;
    --sidebar-border: 0, 0%, 92.2%;
    --sidebar-ring: 0, 0%, 70.8%;

    /* Button colors for light theme */
    --button-primary-bg: oklch(0.091 0 0); /* #171717 */
    --button-primary-text: oklch(1 0 0); /* white */
    --button-primary-disabled-bg: oklch(0.8699 0 0); /* #D4D4D4 */
    --button-primary-disabled-text: oklch(1 0 0);

  }
  
  .dark {
    --page-background: 0 0% 4%; /*  Background color for the entire page //#0A0A0A*/
    --background: 348 0% 4%; /* General background color for containers or sections //#141414*/
    --foreground: 0 0% 98%; /* General text color for high contrast against the background //#f8f8f2*/
    --card: 0 0% 11%; /* Background color for card components */
    --card-foreground: 0 0% 91%; /* Text color for card components */
    --popover: 0 0% 3.9%; /* Background color for popover components */
    --popover-foreground: 0 0% 98%; /* Text color for popover components */
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%; /* Text color for elements with the primary background */
    --secondary: 0 0% 11.2%; /* Secondary color for less prominent UI elements */
    --secondary-foreground: 0 0% 98%; /* Text color for elements with the secondary background */
    --muted: 0 0% 15.9%; /* Background color for muted or less emphasized elements */
    --muted-foreground: 0 0% 62%; /* Text color for muted elements */
    --accent: 0 0% 14%; /* Accent color for highlighting or drawing attention */
    --accent-foreground: 0 0% 98%; /* Text color for elements with the accent background */
    --destructive: 0 100% 31%; /* Color used for destructive actions (e.g., delete buttons) */
    --destructive-foreground: 0 0% 98%; /* Text color for elements with the destructive background */
    --border: 0 0% 24%; /* Color used for borders of elements //#3D3D3D*/
    --input: 0 0% 15.9%; /* Background color for input fields */
    --ring: 0 0% 17%; /* Color used for focus rings or outlines */
    --radius: 0.5rem;
    --chart-1: 220 70% 50%;
    --chart-5: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-2: 340 75% 55%;
    --sidebar-foreground: 0, 0%, 98.5%;
    --sidebar-primary: 264.376, 24.3%, 48.8%;
    --sidebar-primary-foreground: 0, 0%, 98.5%;
    --sidebar-accent: 0, 0%, 26.9%;
    --sidebar-accent-foreground: 0, 0%, 98.5%;
    --sidebar-border: 0, 0%, 10%;
    --sidebar-ring: 0, 0%, 55.6%;

    /* Button colors for dark theme */
    --button-primary-bg: oklch(1 0 0); /* #FFFFFF */
    --button-primary-text: oklch(0.2046 0 0); /* #171717 */
    --button-primary-disabled-bg: oklch(0.3715 0 0); /* #404040 */
    --button-primary-disabled-text: oklch(0.2046 0 0);
    
  }
  
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Main Page Heading Styles */
.main-heading {
  font-family: 'FZ Poppins', var(--font-family);
  font-weight: 600;
  font-size: 28px; /* Exact 36px as per Figma */
  line-height: 36px; /* Hug content - 36px height */
  letter-spacing: -0.025em;
  text-align: center;
  width: 100%; /* Fill container width */
  max-width: 1286px; /* Max width as per design */
  margin: 0 auto 2rem auto; /* Center horizontally */
  color: oklch(0.2046 0 0);
}

.dark .main-heading {
  color: oklch(1 0 0);
}

/* Responsive heading sizes - maintaining 36px */
@media (min-width: 768px) {
  .main-heading {
    margin-bottom: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .main-heading {
    margin-bottom: 3rem;
  }
}

/* Search Input Custom Border */
.search-input {
  border-color: oklch(0.8699 0 0) !important; /* Light mode border */
  width: 1286px !important; /* Fixed width */
  height: 48px !important; /* Fixed height */
  max-width: 100% !important; /* Responsive on smaller screens */
  padding: 12px 16px !important; /* Adjust padding for better proportion */
  font-size: 16px !important;
  line-height: 24px !important;
  box-sizing: border-box !important;
}

/* Responsive search input for mobile */
@media (max-width: 1320px) {
  .search-input {
    width: calc(100vw - 40px) !important; /* Full width minus padding on mobile */
    max-width: 1286px !important;
  }
}

@media (max-width: 768px) {
  .search-input {
    width: calc(100vw - 24px) !important; /* Smaller padding on mobile */
    max-width: 1286px !important;
  }
}

.dark .search-input {
  border-color: oklch(0.3715 0 0) !important; /* Dark mode border */
}

/* Trending Search Tabs */
.trending-search-tab {
  height: 40px !important; /* Fixed height */
  border-radius: 8px !important; /* 8px radius */
  border: 1px solid oklch(0.8699 0 0) !important; /* Light mode border */
  padding: 12px 24px !important; /* Top/bottom: 12px, Left/right: 24px */
  background-color: oklch(1 0 0) !important; /* Light mode background - white */
  color: oklch(0.3715 0 0) !important; /* Light mode text - dark gray */
  font-family: 'FZ Poppins', var(--font-family) !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  white-space: nowrap !important;
}

/* Dark mode trending search tabs */
.dark .trending-search-tab {
  background-color: oklch(0.1448 0 0) !important; /* Dark mode background */
  border-color: oklch(0.3715 0 0) !important; /* Dark mode border */
  color: oklch(0.8699 0 0) !important; /* Dark mode text - light gray */
}

/* Selected state - Light mode */
.trending-search-tab.selected {
  background-color: oklch(0.9219 0 0) !important; /* Light mode selected */
  border-color: oklch(0.8699 0 0) !important;
  color: oklch(0.3715 0 0) !important;
}

/* Selected state - Dark mode */
.dark .trending-search-tab.selected {
  background-color: oklch(0.2686 0 0) !important; /* Dark mode selected */
  border-color: oklch(0.3715 0 0) !important;
  color: oklch(0.8699 0 0) !important;
}

/* Container for trending search tabs with 8px gap */
.trending-search-container {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 8px !important; /* 8px gap between tabs */
  margin-top: 20px !important;
  margin-bottom: 8px !important;
}

/* Custom Template Button - follows normal theme colors */
.custom-template-button {
  width: 226px !important; /* Hug width */
  height: 48px !important; /* Hug height */
  border-radius: 8px !important; /* 8px radius */
  padding: 12px 24px !important; /* Top/bottom: 12px, Left/right: 24px */
  background-color: var(--button-primary-bg) !important;
  color: var(--button-primary-text) !important;
  border: 1px solid var(--button-primary-bg) !important;
  font-family: 'FZ Poppins', var(--font-family) !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  white-space: nowrap !important;
  box-sizing: border-box !important;
}

/* Custom Template Button Hover */
.custom-template-button:hover {
  opacity: 0.9 !important;
}

/* Custom Template Button Disabled */
.custom-template-button:disabled {
  background-color: var(--button-primary-disabled-bg) !important;
  color: var(--button-primary-disabled-text) !important;
  border-color: var(--button-primary-disabled-bg) !important;
  opacity: 0.5 !important;
}

/* Custom Category Button - uses inverted theme colors */
.custom-category-button {
  width: 226px !important; /* Hug width */
  height: 48px !important; /* Hug height */
  border-radius: 8px !important; /* 8px radius */
  padding: 12px 24px !important; /* Top/bottom: 12px, Left/right: 24px */
  background-color: oklch(0.9219 0 0) !important; /* Light theme uses white background */
  color: oklch(0.2046 0 0) !important; /* Light theme uses dark text */
  border: 1px solid oklch(0.9219 0 0) !important; /* Light border */
  font-family: 'FZ Poppins', var(--font-family) !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  white-space: nowrap !important;
  box-sizing: border-box !important;
}

/* Custom Category Button - Dark theme uses light colors */
.dark .custom-category-button {
  background-color: oklch(0.2686 0 0) !important; /* Dark theme uses dark background */
  color: oklch(1 0 0) !important; /* Dark theme uses white text */
  border-color: oklch(0.2686 0 0) !important; /* Dark border */
}

/* Custom Category Button Hover */
.custom-category-button:hover {
  opacity: 0.9 !important;
}

/* Custom Category Button Disabled */
.custom-category-button:disabled {
  opacity: 0.5 !important;
}

/* Container for custom buttons with 8px gap */
.custom-buttons-container {
  display: flex !important;
  gap: 8px !important; /* 8px gap between buttons */
  margin: 16px 0 !important;
}

/* Tool Section Container */
.tool-section-container {
  width: 100% !important;
  max-width: 1286px !important; /* Fill width with max 1286px */
  min-height: 312px !important; /* Hug height 312px */
  display: flex !important;
  flex-direction: column !important; /* Vertical flow */
  margin-bottom: 40px !important; /* 40px gap between sections */
  box-sizing: border-box !important;
}

/* Last section should not have bottom margin */
.tool-section-container:last-child {
  margin-bottom: 0 !important;
}

/* Responsive adjustments for tool sections */
@media (max-width: 1320px) {
  .tool-section-container {
    max-width: calc(100vw - 40px) !important;
  }
}

@media (max-width: 768px) {
  .tool-section-container {
    max-width: calc(100vw - 24px) !important;
    margin-bottom: 32px !important; /* Slightly smaller gap on mobile */
  }
}

/* Tool Card Styling */
.tool-card {
  width: 412.67px !important; /* Fixed width */
  min-height: 120px !important; /* Minimum height */
  height: auto !important; /* Auto height to fit content */
  border-radius: 8px !important; /* 8px radius */
  border: 1px solid oklch(0.8699 0 0) !important; /* Light mode border */
  background-color: oklch(1 0 0) !important; /* Light mode background */
  padding: 24px !important; /* 24px padding all sides */
  display: flex !important;
  align-items: flex-start !important; /* Align to top for better text layout */
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
}

/* Dark mode tool card */
.dark .tool-card {
  background-color: oklch(0.2046 0 0) !important; /* Dark mode background */
  border-color: oklch(0.3715 0 0) !important; /* Dark mode border */
}

/* Tool card icon container */
.tool-card-icon {
  min-width: 72px !important; /* Fixed icon area width */
  max-width: 72px !important;
  height: 72px !important; /* Fixed icon height */
  margin-right: 24px !important; /* Space between icon and text - matching design */
  border-radius: 4px !important; /* 4px border radius to match design */
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 4px !important; /* 4px padding around the image */
  box-sizing: border-box !important;
}

/* Tool card content area */
.tool-card-content {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  min-width: 0 !important; /* Allow text truncation */
}

/* Tool card title */
.tool-card-title {
  font-family: 'FZ Poppins', var(--font-family) !important;
  font-weight: 500 !important; /* Medium weight from Figma */
  font-size: 16px !important; /* Font/Size/md */
  line-height: 24px !important; /* Font/line-height/md (hug 24px) */
  letter-spacing: -0.025em !important; /* Font/Letter-Spacing/xs */
  color: oklch(0.2046 0 0) !important; /* Light mode primary text */
  margin-bottom: 8px !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  width: 253px !important; /* Fill width from Figma */
  max-width: 100% !important; /* Responsive constraint */
  height: 24px !important; /* Hug height from Figma */
}

.dark .tool-card-title {
  color: oklch(1 0 0) !important; /* Dark mode primary text */
}

/* Tool card description */
.tool-card-description {
  font-family: 'FZ Poppins', var(--font-family) !important;
  font-weight: 400 !important; /* Regular weight from Figma */
  font-size: 14px !important; /* Font/Size/sm */
  line-height: 20px !important; /* Font/line-height/sm (hug 20px) */
  letter-spacing: -0.025em !important; /* Font/Letter-Spacing/xs */
  color: oklch(0.3715 0 0) !important; /* Light mode secondary text */
  display: -webkit-box !important;
  -webkit-box-orient: vertical !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  width: 253px !important; /* Fill width from Figma */
  max-width: 100% !important; /* Responsive constraint */
  min-height: 40px !important; /* Minimum height for 2 lines (20px * 2) */
  height: auto !important; /* Auto height to fit content */
}

.dark .tool-card-description {
  color: oklch(0.8699 0 0) !important; /* Dark mode secondary text */
}

/* Tool cards grid container */
.tool-cards-grid {
  display: grid !important;
  grid-template-columns: repeat(3, 412.67px) !important; /* 3 columns with fixed width */
  gap: 24px !important; /* 18px gap between cards */
  justify-content: flex-start !important;
  width: 100% !important;
}

/* Responsive grid adjustments */
@media (max-width: 1320px) {
  .tool-cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr)) !important;
    gap: 20px !important;
  }

  .tool-card {
    width: 100% !important;
    max-width: 412.67px !important;
  }
}

/* iPad mini portrait and small screens - switch to single column earlier */
@media (max-width: 900px) {
  .tool-cards-grid {
    grid-template-columns: 1fr !important; /* Single column when can't fit 2 cards properly */
    gap: 18px !important;
  }

  .tool-card {
    width: 100% !important;
    max-width: none !important; /* Remove max-width constraint for full width */
  }
}

@media (max-width: 768px) {
  .tool-cards-grid {
    grid-template-columns: 1fr !important; /* Single column on mobile */
    gap: 16px !important;
  }
  
  .tool-card {
    width: 100% !important;
    min-height: 120px !important; /* Minimum height to fit 2-line description */
    height: auto !important; /* Auto height to accommodate content */
    padding: 16px !important; /* Smaller padding on mobile */
  }
  
  .tool-card-icon {
    min-width: 56px !important;
    max-width: 56px !important;
    height: 56px !important;
    margin-right: 16px !important; /* Smaller margin on mobile */
    padding: 3px !important; /* Smaller padding on mobile */
  }
  
  .tool-card-title {
    font-size: 14px !important;
    line-height: 18px !important;
  }
  
  .tool-card-description {
    font-size: 12px !important;
    line-height: 16px !important;
    min-height: 32px !important; /* Minimum height for 2 lines on mobile (16px * 2) */
    height: auto !important; /* Auto height to fit content */
  }
}

/* Extra small viewport adjustments (like 768x422) */
@media (max-width: 768px) and (max-height: 500px) {
  .tool-card {
    min-height: 100px !important;
    padding: 12px !important; /* Smaller padding for very small screens */
  }

  .tool-card-icon {
    min-width: 48px !important;
    max-width: 48px !important;
    height: 48px !important;
    margin-right: 12px !important;
    padding: 2px !important;
  }

  .tool-card-title {
    font-size: 13px !important;
    line-height: 16px !important;
    margin-bottom: 4px !important;
  }

  .tool-card-description {
    font-size: 11px !important;
    line-height: 14px !important;
    min-height: 28px !important; /* 2 lines * 14px */
    -webkit-line-clamp: 2 !important;
    line-clamp: 2 !important;
  }
}

.ellipsis-multi-line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 3;
  /* 这里的数字表示你想显示的行数 */
  line-clamp: 3;
  /* 这里的数字表示你想显示的行数 */
}

.main-container {
  max-width: 1286px; /* Updated to match design width */
  padding: 12px;
  transition: width .5s;
  display: flex;
  flex: 1;
}

.dialog-container {
  transition: width .5s;
  max-width: 1286px; /* Updated to match design width */
}

@media (max-width: 984px) {

  .main-container,
  .dialog-container {
    max-width: 95vw;
  }
}


.share-code-container {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 2.15rem;
  bottom: 2.5rem;
  left: 11%;
  right: 11%;
  padding: 1.25rem 2.25rem;
  box-shadow: 50px 50px 100px 10px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  border-radius: 20px;
  border: 1px solid #dedede;
}

.share-code {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

@media (max-width: 768px) {
  .share-code-container {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 0;
  }

  .share-code {
    width: 80%;
  }

  .desktop-boxShadow {
    box-shadow: none;
  }
}

ol,
ul {
  list-style: auto;
  padding-left: 17px;
}

ol p,
ul p {
  display: block;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  unicode-bidi: isolate;
}


table {
  /* width: 50%; */
  border-collapse: collapse;
  /* margin: 50px 0; */
  /* font-size: 18px; */
  text-align: left;
  border: 1px solid #ddd;
}

th,
td {
  padding: 5px;
  border: 1px solid #ddd;
}

tr.hidden {
  display: none;
}

pre {
  white-space: pre-wrap;
}
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 100px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Dark mode adjustments */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid transparent;
  background-clip: padding-box;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.dark * {
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

/* Primary Button Custom Styles */
.btn-primary-custom {
  border: none;
  transition: all 0.2s ease-in-out;
  width: 100%; /* Fill available width */
  height: 48px; /* Fixed height - total 48px */
  min-height: 48px; /* Min height constraint */
  border-radius: 8px; /* Border radius 8px */
  padding: 12px 24px; /* Top/Bottom 12px, Left/Right 24px */
  gap: 8px; /* Gap between icon and text */
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500; /* Medium font weight */
  font-family: 'FZ Poppins', var(--font-family);
  font-size: 14px;
  line-height: 24px; /* Content height 24px */
  white-space: nowrap;
  box-sizing: border-box;
}

/* Enable State - Light Theme */
:root .btn-primary-custom:not(:disabled) {
  background-color: oklch(0.2046 0 0); /* Dark background */
  color: oklch(1 0 0); /* White text */
}

/* Enable State - Dark Theme */
.dark .btn-primary-custom:not(:disabled) {
  background-color: oklch(1 0 0); /* White background */
  color: oklch(0.2046 0 0); /* Dark text */
}

/* Disabled State - Light Theme */
:root .btn-primary-custom:disabled {
  background-color: oklch(0.8699 0 0) !important; /* Light gray background */
  color: oklch(1 0 0) !important; /* White text */
  cursor: not-allowed;
  opacity: 1;
}

/* Disabled State - Dark Theme */
.dark .btn-primary-custom:disabled {
  background-color: oklch(0.3715 0 0) !important; /* Dark gray background */
  color: oklch(0.2046 0 0) !important; /* Dark text */
  cursor: not-allowed;
  opacity: 1;
}

/* Hover Effects */
.btn-primary-custom:hover:not(:disabled) {
  opacity: 0.9;
}

/* Loading State - Same as disabled but with cursor wait */
.btn-primary-custom[data-loading="true"] {
  cursor: wait !important;
}

/* Light Theme Loading */
:root .btn-primary-custom[data-loading="true"] {
  background-color: oklch(0.8699 0 0) !important;
  color: oklch(1 0 0) !important;
}

/* Dark Theme Loading */
.dark .btn-primary-custom[data-loading="true"] {
  background-color: oklch(0.3715 0 0) !important;
  color: oklch(0.2046 0 0) !important;
}

/* Override any conflicting styles */
.btn-primary-custom.btn-primary-custom {
  padding: 12px 24px !important; /* Top/Bottom: 12px, Left/Right: 24px */
  border-radius: 8px !important; /* Border radius: 8px */
  height: 48px !important; /* Total height: 48px */
  min-height: 48px !important;
  line-height: 24px !important; /* Content line height: 24px */
  box-sizing: border-box !important;
}

/* Form Components Disabled States During Loading */

/* Input Fields Disabled State */
.form-loading input:disabled,
.form-loading textarea:disabled {
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

/* Light Theme - Input/Textarea Disabled */
:root .form-loading input:disabled,
:root .form-loading textarea:disabled {
  background-color: oklch(0.8699 0 0) !important; /* Light gray background */
  color: oklch(0.6 0 0) !important; /* Gray text */
  border-color: oklch(0.8699 0 0) !important;
}

/* Dark Theme - Input/Textarea Disabled */
.dark .form-loading input:disabled,
.dark .form-loading textarea:disabled {
  background-color: oklch(0.3715 0 0) !important; /* Dark gray background */
  color: oklch(0.7 0 0) !important; /* Light gray text */
  border-color: oklch(0.3715 0 0) !important;
}

/* Select Components Disabled State */
.form-loading [data-radix-select-trigger]:disabled,
.form-loading .select-disabled {
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

/* Light Theme - Select Disabled */
:root .form-loading [data-radix-select-trigger]:disabled,
:root .form-loading .select-disabled {
  background-color: oklch(0.8699 0 0) !important;
  color: oklch(0.6 0 0) !important;
  border-color: oklch(0.8699 0 0) !important;
}

/* Dark Theme - Select Disabled */
.dark .form-loading [data-radix-select-trigger]:disabled,
.dark .form-loading .select-disabled {
  background-color: oklch(0.3715 0 0) !important;
  color: oklch(0.7 0 0) !important;
  border-color: oklch(0.3715 0 0) !important;
}

/* Button Ghost (Clear Content) Disabled State */
.form-loading .btn-ghost-disabled {
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

/* Light Theme - Ghost Button Disabled */
:root .form-loading .btn-ghost-disabled {
  background-color: transparent !important;
  color: oklch(0.6 0 0) !important;
}

/* Dark Theme - Ghost Button Disabled */
.dark .form-loading .btn-ghost-disabled {
  background-color: transparent !important;
  color: oklch(0.7 0 0) !important;
}

/* Labels Disabled State */
.form-loading label {
  opacity: 0.6 !important;
}

/* Light Theme - Labels Disabled */
:root .form-loading label {
  color: oklch(0.6 0 0) !important;
}

/* Dark Theme - Labels Disabled */
.dark .form-loading label {
  color: oklch(0.7 0 0) !important;
}

/* No horizontal scroll - responsive layout */
.leftsection, .rightsection {
  box-sizing: border-box;
}

/* Ensure container fits viewport */
.relative.flex {
  max-width: 100vw !important;
  overflow-x: hidden !important;
}

/* Responsive breakpoints to prevent horizontal scroll */
@media (max-width: 1600px) {
  .leftsection {
    width: min(400px, 30vw) !important;
    min-width: 280px !important;
  }
}

@media (max-width: 1366px) {
  .leftsection {
    width: min(350px, 28vw) !important;
    min-width: 260px !important;
  }
}

@media (max-width: 1200px) {
  .leftsection {
    width: min(320px, 25vw) !important;
    min-width: 240px !important;
  }

  .rightsection {
    min-width: 300px !important;
  }
}

@media (max-width: 900px) {
  .leftsection {
    width: min(280px, 35vw) !important;
    min-width: 220px !important;
  }

  .rightsection {
    min-width: 250px !important;
  }
}

@media (max-width: 768px) {
  /* Override inline styles for mobile */
  .relative.flex[style*="height: 100vh"] {
    height: auto !important; /* Override inline height */
    max-height: none !important; /* Override inline max-height */
    overflow: visible !important; /* Override inline overflow */
  }

  .relative.flex {
    flex-direction: column !important;
    height: auto !important; /* Auto height for natural scrolling */
    max-height: none !important; /* Remove max-height constraint */
    min-height: 100vh !important; /* Minimum viewport height */
    overflow: visible !important; /* Allow page scrolling */
  }

  /* Override inline styles for left section */
  .leftsection[style*="height: 100vh"] {
    height: auto !important; /* Override inline height */
    max-height: none !important; /* Override inline max-height */
    overflow: visible !important; /* Override inline overflow */
  }

  .leftsection {
    width: 100% !important;
    height: auto !important; /* Auto height - show full content */
    max-height: none !important; /* No height limit */
    border-right: none !important;
    border-bottom: 1px solid var(--border) !important;
    min-width: unset !important;
    overflow: visible !important; /* No internal scrolling */
    padding-bottom: 20px !important; /* Add spacing */
  }

  /* Override inline styles for right section */
  .rightsection[style*="height: 100vh"] {
    height: auto !important; /* Override inline height */
    max-height: none !important; /* Override inline max-height */
    overflow: auto !important; /* Allow scrolling for content */
  }

  .rightsection {
    width: 100% !important;
    height: auto !important; /* Auto height - show full content */
    max-height: none !important; /* No height limit */
    flex: none !important;
    min-width: unset !important;
    overflow: visible !important; /* No internal scrolling */
    padding: 20px !important; /* Add padding for spacing */
  }

  .tool-header {
    padding: 16px !important;
    min-height: 80px !important;
  }

  /* Remove internal scrolling from form containers */
  .leftsection .flex.flex-col.flex-1[style*="calc(100vh"] {
    height: auto !important; /* Override calc height */
    max-height: none !important; /* Override max-height */
    overflow: visible !important; /* Override overflow */
  }

  .leftsection .flex.flex-col.flex-1 {
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
    padding-bottom: 20px !important;
  }

  /* Fix select dropdown - wider content, show 3-4 items */
  [data-radix-select-content] {
    max-height: 140px !important; /* Height for exactly 4 items (35px each) */
    min-width: 250px !important; /* Wider dropdown */
    width: auto !important;
    padding: 2px !important; /* Minimal padding */
  }

  [data-radix-select-viewport] {
    max-height: 136px !important; /* Slightly less than content */
    padding: 0 !important; /* No padding */
    width: 100% !important;
  }

  /* Select items - properly sized and centered */
  [data-radix-select-item],
  [data-radix-select-item].relative.flex,
  [data-radix-select-viewport] > [data-radix-select-item],
  [data-radix-select-viewport] [data-radix-select-item],
  [role="option"] {
    padding: 8px 12px 8px 32px !important; /* Proper padding for alignment */
    min-height: 34px !important; /* Consistent height */
    height: 34px !important; /* Fixed height */
    font-size: 14px !important; /* Readable font */
    line-height: 18px !important; /* Proper line height */
    margin: 0 !important; /* No margin */
    width: 100% !important; /* Full width */
    box-sizing: border-box !important;
    display: flex !important;
    align-items: center !important; /* Center vertically */
    justify-content: flex-start !important; /* Align text to left */
  }

  /* Checkmark icon positioning */
  [data-radix-select-item] span:first-child {
    position: absolute !important;
    left: 8px !important; /* Proper spacing from edge */
    top: 50% !important;
    transform: translateY(-50%) !important; /* Perfect vertical center */
    width: 16px !important;
    height: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Checkmark icon size */
  [data-radix-select-item] span:first-child svg {
    width: 14px !important;
    height: 14px !important;
    height: 28px !important;
  }
}

/* Test rule - make all select items red to verify CSS is working */
@media (max-width: 768px) {
  [data-radix-select-item] * {
    background-color: red !important;
    padding: 2px !important;
    min-height: 20px !important;
    font-size: 10px !important;
  }
}

/* Viewport-fit layout for embedded mode */
.leftsection, .rightsection {
  box-sizing: border-box;
}

/* Responsive width adjustments to fit in viewport */
@media (max-width: 1920px) {
  .leftsection {
    width: min(480px, 35vw) !important;
    min-width: 320px !important;
  }
}

@media (max-width: 1600px) {
  .leftsection {
    width: min(400px, 30vw) !important;
  }
}

@media (max-width: 1366px) {
  .leftsection {
    width: min(350px, 28vw) !important;
  }
}

@media (max-width: 1200px) {
  .leftsection {
    width: min(320px, 25vw) !important;
  }
}

@media (max-width: 768px) {
  .relative.flex {
    flex-direction: column !important;
    height: 100vh !important;
  }

  .leftsection {
    width: 100% !important;
    height: 60vh !important;
    max-height: 60vh !important;
    border-right: none !important;
    border-bottom: 1px solid var(--border) !important;
  }

  .rightsection {
    width: 100% !important;
    height: 40vh !important;
    max-height: 40vh !important;
    flex: none !important;
  }

  .tool-header {
    padding: 16px !important;
    min-height: 60px !important;
  }
}