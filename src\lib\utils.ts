import {
  HEADER_TITLE,
  ILang,
  ILangShort,
  LANG,
  LANG_SHORT,
} from "@/constant/language";
import { Language } from "@/constant/tool_list";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export const locales = ["vi"] as const;

export const getHref = process.env.NEXT_PUBLIC_REGION
  ? process.env.NEXT_PUBLIC_OFFICIAL_WEBSITE_URL_GLOBAL
  : process.env.NEXT_PUBLIC_OFFICIAL_WEBSITE_URL_CHINA;

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getLocalStorage(Item: string) {
  const data = localStorage.getItem(Item);
  return data;
}

export function setLocalStorage(key: string, value: string) {
  localStorage.setItem(key, value);
}

export function setAuthLocalStorage(window: Window, objects: any) {
  if (window) {
    let existedData = JSON.parse(window.localStorage.getItem("DATA") || "{}");
    existedData = {
      ...existedData,
      ...objects,
    };
    window.localStorage.setItem("DATA", JSON.stringify(existedData));
  }
}

export function getAuthLocalStorage(window: Window, key: string) {
  if (window) {
    const data = JSON.parse(window.localStorage.getItem("DATA") || "{}");
    return data[key];
  }
  return null;
}

export const detectLocale = (locale: string): (typeof locales)[number] => {
  const detectedLocale = locale.split("-")[0];
  if (
    ["en", "vi", "zh", "ja"].includes(
      detectedLocale as (typeof locales)[number]
    )
  ) {
    return detectedLocale as (typeof locales)[number];
  }
  return locales[0];
};

type CallbackFunction = (...args: any[]) => void;

export function debounce<T extends CallbackFunction>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null;

  return function (...args: Parameters<T>) {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func(...args);
    }, wait);
  };
}

export function getLanguage() {
  const windowLanguage = window.navigator.language;
  let lang: keyof Language = "english";
  if (["en-US", "zh-CN", "ja-JP", "vi-VN"].includes(windowLanguage)) {
    lang = LANG[windowLanguage as keyof ILang] as keyof Language;
  }
  if (["en", "zh", "ja", "vi"].includes(windowLanguage)) {
    lang = LANG_SHORT[windowLanguage as keyof ILangShort] as keyof Language;
  }
  const localStorageLanguage = localStorage.getItem("lang");
  if (localStorageLanguage)
    lang = localStorageLanguage as
      | "chinese"
      | "english"
      | "japanese"
      | "vietnamese";
  const locale = window.location.pathname.split("/")[1] as keyof ILangShort;
  if (locale) {
    if (["en", "zh", "ja", "vi"].includes(locale))
      lang = LANG_SHORT[locale] as keyof Language;
    else lang = "english";
  }
  const searchLang = new URLSearchParams(window.location.search).get(
    "lang"
  ) as keyof ILang;
  if (searchLang) {
    if (["en-US", "zh-CN", "ja-JP", "vi-VN"].includes(searchLang))
      lang = LANG[searchLang] as keyof Language;
    else lang = "english";
  }
  document.title = HEADER_TITLE[lang] ?? HEADER_TITLE.english;
  localStorage.setItem("lang", lang);
  return lang;
}

export const getCookie = (name: string): string | undefined => {
  const cookies = document.cookie
    .split("; ")
    .map((cookie) => cookie.split("="));
  const match = cookies.find(([key]) => key === name);
  return match ? decodeURIComponent(match[1]) : undefined;
};

export const catchErrorHandle = async <T>(
  promise: Promise<T>
): Promise<[undefined, T] | [Error | any]> => {
  return promise
    .then((data) => {
      return [undefined, data] as [undefined, T];
    })
    .catch((error) => {
      return [error];
    });
};
